package com.sinoyd.lims.lim.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.base.configuration.FilePathConfig;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.lims.lim.dto.customer.DtoOcrDataContainer;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfig;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParam;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigParamData;
import com.sinoyd.lims.lim.dto.lims.DtoOcrConfigRecord;
import com.sinoyd.lims.lim.repository.lims.OcrConfigParamDataRepository;
import com.sinoyd.lims.lim.repository.lims.OcrConfigParamRepository;
import com.sinoyd.lims.lim.repository.lims.OcrConfigRecordRepository;
import com.sinoyd.lims.lim.repository.lims.OcrConfigRepository;
import com.sinoyd.lims.lim.service.OcrConfigService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;


/**
 * ocr对象接口实现
 *
 * <AUTHOR>
 * @version V1.0.0 2023/11/27
 * @since V100R001
 */
@Service
@Slf4j
public class OcrAiParseServiceImpl extends BaseJpaServiceImpl<DtoOcrConfig, String, OcrConfigRepository> implements OcrConfigService {

    private OcrConfigParamRepository ocrConfigParamRepository;

    private OcrConfigParamDataRepository ocrConfigParamDataRepository;

    private OcrConfigRecordRepository ocrConfigRecordRepository;

    private RestTemplate restTemplate;

    private FilePathConfig filePathConfig;

    @Value("${ocr.url:http}")
    private String ocrUrl;

    @Value("${ocr.prompt:角色：你是位资深的光学数据识别专家,任务：你需要识别图片内容并将内容按照指定的json格式输出,要求,1.禁止将中文转成英文,禁止将单位转成中文,2.数据中的*需要去掉,3.数据不需要单位,4.采样日期和采样时间不需要分开。指定的json格式示例如下{参数名称1:参数值1,参数名称2:参数值2},去掉不必要的换行符,生成的json文本要能支持直接转换为java中的JSONObject,回答不需要其余说明,只需要输出json文本。}")
    private String ocrPrompt;

    @Override
    @Transactional
    public void callStreamApi(DtoOcrConfigRecord ocrConfigRecord, Consumer<String> onData) {
        //存储容器
        DtoOcrDataContainer container = new DtoOcrDataContainer();
        List<DtoOcrConfigParamData> initDatalist = new ArrayList<>();
        //待检测参数
        List<DtoOcrConfigParam> paramList = ocrConfigParamRepository.findAllByConfigId(ocrConfigRecord.getConfigId())
                .stream().sorted(Comparator.comparing(DtoOcrConfigParam::getOrderNum, Comparator.reverseOrder())).collect(Collectors.toList());
        //提示词
        DtoOcrConfig ocrConfig = repository.findOne(ocrConfigRecord.getConfigId());

        //调用已经封装好的AI解析接口
        File file = new File(filePathConfig.getFilePath() + ocrConfigRecord.getFilePath());
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("prompt", StringUtil.isNotEmpty(ocrConfig.getPrompt()) ? ocrConfig.getPrompt() : ocrPrompt)
                .addFormDataPart("image_file", file.getName(),
                        RequestBody.create(MediaType.parse("application/octet-stream"), file))
                .build();
        Request request = new Request.Builder()
                .url(ocrUrl)
                .post(body)
                .build();
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new RuntimeException("API call failed: " + response.code());
            }
            try (ResponseBody responseBody = response.body();
                 BufferedReader reader = new BufferedReader(new InputStreamReader(responseBody.byteStream(),
                         StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (!line.trim().isEmpty() && !line.contains("id:")) {
                        String json = line.replace("data: ", "");
                        try {
                            Boolean done = JSONObject.parseObject(json).getBoolean("done");
                            if (done) {
                                JSONObject originObject = JSONObject.parseObject(json).getJSONObject("workflow_result");
                                container.setAiAnswer(originObject.toJSONString());
                                container.setRecordId(ocrConfigRecord.getId());
                                //数据转换
                                parseOriginData(ocrConfigRecord, initDatalist, paramList, container);
                                //初始数据保存
                                ocrConfigRecordRepository.save(ocrConfigRecord);
                                ocrConfigParamDataRepository.save(initDatalist);
                                //存储容器
                                container.setDataList(initDatalist);
                                String endStr = JsonUtil.toJson(container);
                                // 立即处理每一行数据，不等待所有数据读取完成
                                onData.accept(endStr);
                            } else {
                                // 立即处理每一行数据，不等待所有数据读取完成
                                onData.accept(json);
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                            throw new BaseException("AI识别异常");
                        }
                        // 添加小延迟，确保数据能够及时发送
                        try {
                            Thread.sleep(10);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Stream API call failed", e);
            throw new RuntimeException("Stream API call failed", e);
        }
    }

    /**
     * 数据转换
     *
     * @param ocrConfigRecord 参数容器
     * @param initDatalist    存储容器
     * @param paramList       待检测参数
     * @param container       解析对象
     */
    private void parseOriginData(DtoOcrConfigRecord ocrConfigRecord, List<DtoOcrConfigParamData> initDatalist, List<DtoOcrConfigParam> paramList, DtoOcrDataContainer container) {
        ocrConfigRecord.setOriginData(container.getOcrParse());
        ocrConfigRecord.setAiAnswer(container.getAiAnswer());
        JSONObject object = JSONObject.parseObject(container.getAiAnswer());
        for (DtoOcrConfigParam dtoOcrConfigParam : paramList) {
            DtoOcrConfigParamData configParamData = new DtoOcrConfigParamData();
            configParamData.setRecordId(ocrConfigRecord.getId());
            configParamData.setConfigParamId(dtoOcrConfigParam.getId());
            configParamData.setDimension(dtoOcrConfigParam.getDimension());
            configParamData.setParamName(dtoOcrConfigParam.getParamName());
            String saveValue = "";
            if (object.containsKey(dtoOcrConfigParam.getParamNameAlias()) && object.get(dtoOcrConfigParam.getParamNameAlias()) != null) {
                saveValue = object.get(dtoOcrConfigParam.getParamNameAlias()).toString();
            }
            configParamData.setOrgId(ocrConfigRecord.getOrgId());
            configParamData.setCreator(ocrConfigRecord.getCreator());
            configParamData.setSaveValue(saveValue);
            initDatalist.add(configParamData);
        }
    }

    /**
     * 字符串匹配
     * @param fullText              全文本
     * @param dtoOcrConfigParam     识别参数
     * @return 匹配结果
     */
    private String analyzeText(String fullText,DtoOcrConfigParam dtoOcrConfigParam){
        if(StringUtil.isNotEmpty(fullText)){
            //开始字符串未填写时 默认为别名
            String regexBegin = StringUtil.isNotEmpty(dtoOcrConfigParam.getRegexBegin())?dtoOcrConfigParam.getRegexBegin():dtoOcrConfigParam.getParamNameAlias();
            //拆解多个开始字符
            List<String> beginStrList = Arrays.asList(regexBegin.split("or"));
            beginStrList = beginStrList.stream().map(p->p.trim().replaceAll("：",":")).collect(Collectors.toList());
            boolean hasEnd = StringUtil.isNotEmpty(dtoOcrConfigParam.getRegexEnd());
            List<String> endStrList = new ArrayList<>();
            int maxLength = dtoOcrConfigParam.getMaxLength()!=null?dtoOcrConfigParam.getMaxLength():20;
            if(hasEnd){
                endStrList = Arrays.asList(dtoOcrConfigParam.getRegexEnd().split("or"));
                endStrList = endStrList.stream().map(p->p.trim().replaceAll("：",":")).collect(Collectors.toList());
            }
            //匹配
            fullText = fullText.replaceAll("：",":");
            for (String beginStr:beginStrList) {
                if(fullText.contains(beginStr)){
                    String tempStrStartWithBegin = fullText.substring(fullText.indexOf(beginStr));
                    if(hasEnd){
                        for (String endStr:endStrList) {
                            if(tempStrStartWithBegin.contains(endStr)){
                                //匹配到结尾但超长度 返回最大长度文本
                                String targetText = tempStrStartWithBegin.substring(beginStr.length(),tempStrStartWithBegin.indexOf(endStr));
                                if(targetText.length()>=maxLength){
                                    return tempStrStartWithBegin.substring(beginStr.length(),maxLength);
                                }
                                return targetText;
                            }
                        }
                    }
                    //没有匹配到结尾 返回最大长度文本
                    if(tempStrStartWithBegin.length()>=maxLength){
                        return tempStrStartWithBegin.substring(beginStr.length(),maxLength);
                    }
                }
            }
        }
        return "";
    }


    @Autowired
    public void setOcrConfigParamRepository(OcrConfigParamRepository ocrConfigParamRepository) {
        this.ocrConfigParamRepository = ocrConfigParamRepository;
    }

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }


    @Autowired
    public void setOcrConfigParamDataRepository(OcrConfigParamDataRepository ocrConfigParamDataRepository) {
        this.ocrConfigParamDataRepository = ocrConfigParamDataRepository;
    }

    @Autowired
    public void setFilePathConfig(FilePathConfig filePathConfig) {
        this.filePathConfig = filePathConfig;
    }

    @Autowired
    public void setOcrConfigRecordRepository(OcrConfigRecordRepository ocrConfigRecordRepository) {
        this.ocrConfigRecordRepository = ocrConfigRecordRepository;
    }
}
