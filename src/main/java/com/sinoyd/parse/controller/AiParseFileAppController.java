package com.sinoyd.parse.controller;

import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.parse.service.AiParseFileAppService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * AI仪器解析应用服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@RestController
@RequestMapping("api/parse/aiParseFileApp")
public class AiParseFileAppController extends BaseJpaController<DtoAiParseFileApp, String, AiParseFileAppService> {


}
