package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.ParseDocumentCriteria;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.parse.service.ParseDocumentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 仪器解析文件管理服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Validated
@Api(tags = "仪器解析文件管理服务")
@RestController
@RequestMapping("api/parse/parseDocument")
public class ParseDocumentController extends BaseJpaController<DtoParseDocument, String, ParseDocumentService> {

    /**
     * 分页动态条件查询仪器解析文件
     *
     * @param parseDocumentCriteria 条件参数
     * @return RestResponse<List<DtoParseDocument>>
     */
    @ApiOperation(value = "分页动态条件查询仪器解析文件", notes = "分页动态条件查询仪器解析文件")
    @GetMapping
    public RestResponse<List<DtoParseDocument>> findByPage(ParseDocumentCriteria parseDocumentCriteria) {
        PageBean<DtoParseDocument> pageBean = super.getPageBean();
        RestResponse<List<DtoParseDocument>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, parseDocumentCriteria);
        restResponse.setRestStatus(StringUtil.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 根据对象ID查询文档列表
     *
     * @param objectId 对象ID
     * @return RestResponse<List<DtoParseDocument>>
     */
    @ApiOperation(value = "根据对象ID查询文档列表", notes = "根据对象ID查询文档列表")
    @GetMapping("/object/{objectId}")
    public RestResponse<List<DtoParseDocument>> findByObjectId(@PathVariable String objectId) {
        RestResponse<List<DtoParseDocument>> restResponse = new RestResponse<>();
        List<DtoParseDocument> result = service.findByObjectId(objectId);
        restResponse.setRestStatus(StringUtil.isEmpty(result) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 删除仪器解析文件
     *
     * @param id 文件ID
     * @return RestResponse<String>
     */
    @ApiOperation(value = "删除仪器解析文件", notes = "删除仪器解析文件")
    @DeleteMapping("/{id}")
    public RestResponse<String> delete(@PathVariable String id) {
        RestResponse<String> restResponse = new RestResponse<>();
        service.delete(id);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData("删除成功");
        return restResponse;
    }
}
