package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.ParseDocument;
import javax.persistence.*;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

/**
 * 仪器解析文件管理DTO实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PARSE_ParseDocument")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
@ApiModel(description = "仪器解析文件管理DTO实体")
public class DtoParseDocument extends ParseDocument {
    
    private static final long serialVersionUID = 1L;
}
