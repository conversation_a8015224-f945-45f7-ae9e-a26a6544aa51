package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.parse.enums.EnumParseLogStatus;
import com.sinoyd.parse.enums.EnumParseType;
import com.sinoyd.parse.repository.AiParseFileAppRepository;
import com.sinoyd.parse.service.AiParseFileAppService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * AI仪器解析应用操作接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Service
public class AiParseFileAppServiceImpl extends BaseJpaServiceImpl<DtoAiParseFileApp, String, AiParseFileAppRepository> implements AiParseFileAppService {

    @Override
    public void findByPage(PageBean<DtoAiParseFileApp> pb, BaseCriteria aiParseFileAppCriteria) {
        pb.setEntityName("DtoAiParseFileApp a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, aiParseFileAppCriteria);
        
        // 设置解析方式名称和解析状态名称
        List<DtoAiParseFileApp> appList = pb.getData();
        Map<String, String> parseTypeMap = EnumParseType.getMapData();
        Map<String, String> parseStatusMap = EnumParseLogStatus.getMapData();
        
        for (DtoAiParseFileApp app : appList) {
            // 设置解析方式名称
            if (app.getParseType() != null) {
                String parseTypeName = parseTypeMap.getOrDefault(app.getParseType().toString(), "");
                app.setParseTypeName(parseTypeName);
            }
            
            // 设置解析状态名称
            if (app.getParseStatus() != null) {
                String parseStatusName = parseStatusMap.getOrDefault(app.getParseStatus().toString(), "");
                app.setParseStatusName(parseStatusName);
            }
        }
    }

    @Override
    @Transactional
    public DtoAiParseFileApp save(DtoAiParseFileApp entity) {
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp update(DtoAiParseFileApp entity) {
        return super.update(entity);
    }
}
