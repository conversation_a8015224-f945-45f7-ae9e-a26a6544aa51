package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtil;
import com.sinoyd.frame.base.util.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.parse.repository.ParseDocumentRepository;
import com.sinoyd.parse.service.ParseDocumentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 仪器解析文件管理操作接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Service
public class ParseDocumentServiceImpl extends BaseJpaServiceImpl<DtoParseDocument, String, ParseDocumentRepository> implements ParseDocumentService {

    @Override
    public void findByPage(PageBean<DtoParseDocument> pb, BaseCriteria parseDocumentCriteria) {
        pb.setEntityName("DtoParseDocument a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, parseDocumentCriteria);
    }

    @Override
    @Transactional
    public DtoParseDocument save(DtoParseDocument entity) {
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoParseDocument update(DtoParseDocument entity) {
        return super.update(entity);
    }

    @Override
    public List<DtoParseDocument> findByObjectId(String objectId) {
        if (StringUtil.isEmpty(objectId)) {
            return null;
        }
        return repository.findByObjectId(objectId);
    }
}
