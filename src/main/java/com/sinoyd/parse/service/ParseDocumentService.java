package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.frame.service.IBaseJpaService;

import java.util.List;

/**
 * 仪器解析文件管理操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
public interface ParseDocumentService extends IBaseJpaService<DtoParseDocument, String> {

    /**
     * 根据对象ID查询文档列表
     *
     * @param objectId 对象ID
     * @return 文档列表
     */
    List<DtoParseDocument> findByObjectId(String objectId);
}
